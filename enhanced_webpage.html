<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Webpage with GenAI Elements</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        /* Top Header */
        .top-header {
            background-color: #1e4a8c;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-header h1 {
            font-size: 18px;
            font-weight: normal;
        }

        .top-header-right {
            background-color: #2563eb;
            padding: 8px 15px;
            border: 1px solid #60a5fa;
            font-size: 14px;
        }

        /* Navigation Bar */
        .nav-bar {
            background-color: #000;
            color: white;
            padding: 8px 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            font-size: 14px;
        }

        .nav-item:hover {
            background-color: #333;
        }

        .nav-dropdown {
            background-color: #333;
            color: white;
            padding: 8px 15px;
            border: 1px solid #666;
            font-size: 14px;
        }

        /* Search Bar */
        .search-container {
            position: absolute;
            top: 70px;
            right: 20px;
        }

        .search-bar {
            padding: 5px 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            width: 200px;
        }

        /* Main Container */
        .main-container {
            display: flex;
            margin-top: 20px;
            gap: 20px;
            padding: 0 20px;
        }

        /* Left Sidebar */
        .left-sidebar {
            width: 150px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background-color: #1e4a8c;
            color: white;
            padding: 8px 15px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-align: left;
        }

        .nav-button:hover {
            background-color: #2563eb;
        }

        .dropdown-section {
            border: 1px solid #ccc;
            padding: 15px;
            background-color: white;
            margin-top: 10px;
        }

        .dropdown-title {
            color: #666;
            font-size: 12px;
            text-align: center;
            line-height: 1.3;
        }

        /* Content Area */
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Banner Section */
        .banner-section {
            background-color: #1e4a8c;
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .banner-title {
            border: 1px solid #60a5fa;
            padding: 10px 20px;
            display: inline-block;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .banner-content {
            font-size: 16px;
        }

        /* Main Content */
        .main-content {
            background-color: white;
            padding: 30px;
            position: relative;
        }

        .content-label {
            position: absolute;
            top: -10px;
            right: 20px;
            background-color: #e5e7eb;
            border: 1px solid #9ca3af;
            padding: 5px 10px;
            font-size: 12px;
            color: #666;
        }

        .welcome-title {
            text-align: center;
            font-size: 20px;
            margin-bottom: 20px;
            color: #333;
        }

        .content-text {
            text-align: center;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        /* Table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th {
            background-color: #1e4a8c;
            color: white;
            padding: 10px;
            text-align: left;
            border: 1px solid #333;
        }

        .data-table td {
            padding: 8px 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }

        .data-table tr:nth-child(even) td {
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <h1>Enhanced Webpage with GenAI Elements</h1>
        <div class="top-header-right">Top header</div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
        <a href="#" class="nav-item">Home</a>
        <a href="#" class="nav-item">Services</a>
        <a href="#" class="nav-item">About Us</a>
        <a href="#" class="nav-item">Contact</a>
        <div class="nav-dropdown">Global navigation with dropdowns</div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
        <input type="text" class="search-bar" placeholder="Search...">
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Left Sidebar -->
        <div class="left-sidebar">
            <button class="nav-button">Btn 1</button>
            <button class="nav-button">Btn 2</button>
            <button class="nav-button">Btn 3</button>
            
            <div class="dropdown-section">
                <div class="dropdown-title">Left navigation<br>with drop down<br>values</div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Banner Section -->
            <div class="banner-section">
                <div class="banner-title">Center banners with rotation effect</div>
                <div class="banner-content">Banner 3: Contact us for more info</div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-label">Body text and table with cell merge</div>
                
                <h2 class="welcome-title">Welcome to the Enhanced Webpage</h2>
                
                <div class="content-text">
                    This is a placeholder for dynamic content. Use this area to showcase different sections of the site, provide information, or link to additional resources.<br><br>
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent sed sapiente anim quas optio, hic veritam. Quisquam quas quis, quis laborum incidunt fugiat quas debitis velit sunt vitae quas. Hic placerat architecto corporis, vel elit quo molestiae magnam repellendus commodi!
                </div>

                <!-- Data Table -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Header 1</th>
                            <th>Header 2</th>
                            <th>Header 3</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Row 1, Col 1</td>
                            <td>Row 1, Col 2</td>
                            <td>Row 1, Col 3</td>
                        </tr>
                        <tr>
                            <td rowspan="2">Row 2 & 3, Col 1 merged</td>
                            <td>Row 2, Col 2</td>
                            <td>Row 2, Col 3</td>
                        </tr>
                        <tr>
                            <td>Row 3, Col 2</td>
                            <td>Row 3, Col 3</td>
                        </tr>
                        <tr>
                            <td>Row 4, Col 1</td>
                            <td colspan="2">Row 4, Col 2 & Col 3 merged</td>
                        </tr>
                        <tr>
                            <td>Row 5, Col 1</td>
                            <td>Row 5, Col 2</td>
                            <td>Row 5, Col 3</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
